// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

// Convert a FHairSample into a FGufferData
FGBufferData HairSampleToGBufferData(in FHairSample In, in float InDualScatteringRoughnessOverride=0)
{
	FGBufferData Out = (FGBufferData)0;
	Out.ShadingModelID = SHADINGMODELID_HAIR;
	Out.WorldNormal = In.Tangent;
	Out.BaseColor = In.BaseColor;
	Out.Roughness = In.Roughness;
	Out.Specular = In.Specular;
	Out.Metallic = 0; // Scattering;
	Out.Depth = ConvertFromDeviceZ(In.Depth);
	Out.GBufferAO = 1;
	Out.CustomData = float4(InDualScatteringRoughnessOverride, 0, In.Backlit, 0);
	Out.IndirectIrradiance = 1000000;
	Out.DiffuseColor = 0;
	Out.SpecularColor = 0;
	Out.PrecomputedShadowFactors = 1;
	Out.PerObjectGBufferData = HasHairFlags(In.Flags, HAIR_FLAGS_CAST_CONTACT_SHADOW) ? 1u : 0u;
	return Out;
}