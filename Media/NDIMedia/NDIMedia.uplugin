{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "NDI Media", "Description": "Implements media output using NDI protocol", "Category": "Media Players", "CreatedBy": "Epic Games", "CreatedByURL": "http://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": true, "EnabledByDefault": false, "SupportedTargetPlatforms": ["Win64"], "Modules": [{"Name": "NDIMedia", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "NDIMediaEditor", "Type": "Editor", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64"]}, {"Name": "NDIMediaRendering", "Type": "Runtime", "LoadingPhase": "PostConfigInit", "PlatformAllowList": ["Win64"]}], "Plugins": [{"Name": "MediaIOFramework", "Enabled": true}, {"Name": "MediaPlayerEditor", "Enabled": true}]}