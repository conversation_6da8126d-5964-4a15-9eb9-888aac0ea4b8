// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "MuCOE/Nodes/CustomizableObjectNode.h"
#include "MuT/NodeModifier.h"

#include "CustomizableObjectNodeModifierBase.generated.h"

enum class EMutableMultipleTagPolicy:uint8;


USTRUCT()
struct FLegacyTag
{
	GENERATED_BODY()

	UPROPERTY()
	TSoftObjectPtr<UCustomizableObject> ParentObject;

	UPROPERTY()
	FGuid ParentNode;

	UPROPERTY()
	FString Tag;

	bool operator==(const FLegacyTag&) const = default;
};


UCLASS(abstract)
class CUSTOMIZABLEOBJECTEDITOR_API UCustomizableObjectNodeModifierBase : public UCustomizableObjectNode
{

	GENERATED_BODY()

public:

	/** Materials in all other objects that activate this tags will be affected by this modifier. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Modifier)
	TArray<FString> RequiredTags;

	/** Policy to use required tags in case more than one is added. */
	UPROPERTY(EditAnywhere, Category = Modifier)
	EMutableMultipleTagPolicy MultipleTagPolicy = EMutableMultipleTagPolicy::OnlyOneRequired;

public:

	// EdGraphNode interface
	virtual FLinearColor GetNodeTitleColor() const override;

	// UCustomizableObjectNode interface
	virtual void BackwardsCompatibleFixup(int32 CustomizableObjectCustomVersion) override;
	virtual void PostBackwardsCompatibleFixup() override;
	virtual void AllocateDefaultPins(UCustomizableObjectNodeRemapPins* RemapPins) override;

	// Own interface
	virtual UEdGraphPin* GetOutputPin() const;
	virtual UEdGraphPin* RequiredTagsPin() const;
	TArray<FString> GetNodeRequiredTags(TArray<const UCustomizableObjectNodeMacroInstance*>* MacroContext = nullptr) const;

	/** Return true if the tags and policy in this node would make this node modify the given parameter node. */
	bool IsApplicableTo(UCustomizableObjectNode* Candidate);

	/** Get the list of all nodes of this object that may be affected by this modifier. This is an approximate
	* query: it includes all nodes that may be affected by this modifier but it may include nodes that will not 
	* be modified by this modifier because of other conditions.
	*/
	void GetPossiblyModifiedNodes(TArray<UCustomizableObjectNode*>&);

	/** Generate a tag that is unique and stable for the given node. */
	static FString MakeNodeAutoTag(UEdGraphNode*);

protected:

	/** This is a list of tags autogenerated due to backwards compatibility upgrade. 
	* It must be removed when we remove the related backwards compatibility code.
	*/
	UPROPERTY()
	TArray<FLegacyTag> LegacyBackportsRequiredTags;

private:

	UPROPERTY()
	FEdGraphPinReference RequiredTagsPinRef;

};

