#usda 1.0
(
    "WARNING: THIS FILE IS GENERATED BY usdGenSchema.  DO NOT EDIT."
)

class "MaterialXConfigAPI" (
    doc = """MaterialXConfigAPI is an API schema that provides an interface for
    storing information about the MaterialX environment.

    Initially, it only exposes an interface to record the MaterialX library
    version that data was authored against. The intention is to use this
    information to allow the MaterialX library to perform upgrades on data
    from prior MaterialX versions.
    """
)
{
    string config:mtlx:version = "1.38" (
        doc = """MaterialX library version that the data has been authored
        against. Defaults to 1.38 to allow correct verisoning of old files."""
    )
}

