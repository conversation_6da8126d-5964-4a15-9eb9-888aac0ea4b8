{"Plugins": [{"Info": {"Validators": {"CompositionErrorTest": {"doc": "Validator aims at providing all composition errors, which were generated while composing the stage."}, "StageMetadataChecker": {"doc": "Stages that can be consumed as referenceable assets must have a valid 'defaultPrim' specified."}, "keywords": ["UsdCoreValidators"]}}, "LibraryPath": "../../../../../Source/ThirdParty/Linux/bin/x86_64-unknown-linux-gnu/libusd_usdValidation.so", "Name": "usdValidation", "ResourcePath": "resources", "Root": "..", "Type": "library"}]}