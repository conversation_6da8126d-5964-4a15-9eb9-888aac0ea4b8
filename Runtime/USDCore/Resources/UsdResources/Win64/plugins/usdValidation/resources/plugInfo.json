{"Plugins": [{"Info": {"Validators": {"CompositionErrorTest": {"doc": "Validator aims at providing all composition errors, which were generated while composing the stage."}, "StageMetadataChecker": {"doc": "Stages that can be consumed as referenceable assets must have a valid 'defaultPrim' specified."}, "keywords": ["UsdCoreValidators"]}}, "LibraryPath": "../../../../../../../../Binaries/Win64/usd_usdValidation.dll", "Name": "usdValidation", "ResourcePath": "resources", "Root": "..", "Type": "library"}]}