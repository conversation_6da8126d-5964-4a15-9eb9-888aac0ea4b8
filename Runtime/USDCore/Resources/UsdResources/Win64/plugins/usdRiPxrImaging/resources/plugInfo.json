{"Plugins": [{"Info": {"Types": {"UsdRiPxrImagingAovLightAdapter": {"bases": ["UsdImagingLightAdapter"], "isInternal": true, "primTypeName": "PxrAovLight"}, "UsdRiPxrImagingDisplayFilterAdapter": {"bases": ["UsdImagingPrimAdapter"], "isInternal": true, "primTypeName": "PxrDisplayFilterPluginBase", "includeDerivedPrimTypes": true}, "UsdRiPxrImagingIntegratorAdapter": {"bases": ["UsdImagingPrimAdapter"], "isInternal": true, "primTypeName": "PxrIntegratorPluginBase", "includeDerivedPrimTypes": true}, "UsdRiPxrImagingSampleFilterAdapter": {"bases": ["UsdImagingPrimAdapter"], "isInternal": true, "primTypeName": "PxrSampleFilterPluginBase", "includeDerivedPrimTypes": true}, "UsdRiPxrImagingCameraAPIAdapter": {"bases": ["UsdImagingAPISchemaAdapter"], "isInternal": true, "apiSchemaName": "PxrCameraAPI"}}}, "LibraryPath": "../../../../../../../../Binaries/Win64/usd_usdRiPxrImaging.dll", "Name": "usdRiPxrImaging", "ResourcePath": "resources", "Root": "..", "Type": "library"}]}