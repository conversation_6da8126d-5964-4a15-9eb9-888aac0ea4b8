# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "Types": {
                    "UsdProcGenerativeProcedural": {
                        "alias": {
                            "UsdSchemaBase": "GenerativeProcedural"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomBoundable"
                        ], 
                        "schemaIdentifier": "GenerativeProcedural", 
                        "schemaKind": "concreteTyped"
                    }
                }
            }, 
            "LibraryPath": "../../../../../Source/ThirdParty/Mac/bin/libusd_usdProc.dylib", 
            "Name": "usdProc", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
