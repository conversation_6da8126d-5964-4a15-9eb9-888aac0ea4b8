-- glslfx version 0.1

//
// Copyright 2016 Pixar
//
// Licensed under the terms set forth in the LICENSE.txt file available at
// https://openusd.org/license.
//

--- This is what an import might look like.
--- #import $TOOLS/hdx/shaders/renderPassShadowShader.glslfx

#import $TOOLS/hdSt/shaders/renderPass.glslfx

-- configuration
{
    "techniques": {
        "default": {
            "vertexShader" : {
                "source": [ "RenderPass.Camera",
                            "RenderPass.ApplyClipPlanes" ]
            },
            "postTessControlShader" : {
                "source": [ "RenderPass.Camera" ]
            },
            "postTessVertexShader" : {
                "source": [ "RenderPass.Camera",
                            "RenderPass.ApplyClipPlanes" ]
            },
            "tessControlShader" : {
                "source": [ "RenderPass.Camera" ]
            },
            "tessEvalShader" : {
                "source": [ "RenderPass.Camera",
                            "RenderPass.ApplyClipPlanes" ]
            },
            "geometryShader" : {
                "source": [ "RenderPass.Camera",
                            "RenderPass.ApplyClipPlanes" ]
            },
            "fragmentShader" : {
                "source": [ "RenderPass.Camera",
                            "RenderPass.CameraFS",
                            "RenderPass.NoSelection",
                            "RenderPass.NoColorOverrides",
                            "RenderPass.RenderColor",
                            "RenderPass.RenderIdNoOp",
                            "RenderPass.RenderNeyeNoOp",
                            "RenderPass.RenderOutput" ]
            }
        }
    }
}
