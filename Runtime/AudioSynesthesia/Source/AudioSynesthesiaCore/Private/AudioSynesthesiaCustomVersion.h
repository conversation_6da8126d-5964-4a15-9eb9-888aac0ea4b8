// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Misc/Guid.h"

// Custom serialization version for all packages containing AudioSynesthesia asset types
struct FAudioSynesthesiaCustomVersion
{
	enum Type
	{
		// Before any version changes were made in the module
		BeforeCustomVersionWasAdded = 0,

		// -----<new versions can be added above this line>-------------------------------------------------
		VersionPlusOne,
		LatestVersion = VersionPlusOne - 1
	};

	// The GUID for this custom version number
	const static FGuid GUID;

	FAudioSynesthesiaCustomVersion() = delete;
};
