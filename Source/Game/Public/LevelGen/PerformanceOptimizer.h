#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "PerformanceOptimizer.generated.h"

// 前向声明
class UMassRepresentationSubsystem;
struct FMassActorSpawnRequestHandle;

// 【并行处理升级】并行计算支持
#include "HAL/ThreadSafeBool.h"
#include <atomic>

// 【内存池高级优化】分层内存池支持
#include "HAL/CriticalSection.h"
#include "Containers/LockFreeList.h"
#include <memory_resource>

/**
 * 缓存级别枚举
 */
UENUM(BlueprintType)
enum class ECacheLevel : uint8
{
    L1_Memory = 0,      // 内存缓存（最快）
    L2_Compressed,      // 压缩缓存（中等）
    L3_Disk,           // 磁盘缓存（最慢）
    MAX UMETA(Hidden)
};

// 【已删除】分层内存池枚举和结构 - 已被统一对象池替代

/**
 * LOD级别枚举
 */
UENUM(BlueprintType)
enum class ELODLevel : uint8
{
    LOD0_Highest = 0,   // 最高细节
    LOD1_High,          // 高细节
    LOD2_Medium,        // 中等细节
    LOD3_Low,           // 低细节
    LOD4_Lowest,        // 最低细节
    MAX UMETA(Hidden)
};

/**
 * 性能统计信息结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FPerformanceStats
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Stats")
    float FrameTime = 0.0f;             // 帧时间（毫秒）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Stats")
    float MemoryUsage = 0.0f;           // 内存使用量（MB）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Stats")
    int32 ActiveMapCells = 0;           // 活跃地图格子数量

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Stats")
    int32 CachedObjects = 0;            // 缓存对象数量

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Stats")
    int32 PooledObjects = 0;            // 对象池中对象数量

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Stats")
    float CacheHitRate = 0.0f;          // 缓存命中率

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Stats")
    float UpdateTime = 0.0f;            // 更新时间（毫秒）

    FPerformanceStats()
    {
        FrameTime = 0.0f;
        MemoryUsage = 0.0f;
        ActiveMapCells = 0;
        CachedObjects = 0;
        PooledObjects = 0;
        CacheHitRate = 0.0f;
        UpdateTime = 0.0f;
    }
};

/**
 * 缓存条目结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FCacheEntry
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    FString Key = TEXT("");             // 缓存键

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    ECacheLevel Level = ECacheLevel::L1_Memory; // 缓存级别

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    float LastAccessTime = 0.0f;        // 最后访问时间

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    int32 AccessCount = 0;              // 访问次数

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    int32 DataSize = 0;                 // 数据大小（字节）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    bool bIsCompressed = false;         // 是否压缩

    FCacheEntry()
    {
        Key = TEXT("");
        Level = ECacheLevel::L1_Memory;
        LastAccessTime = 0.0f;
        AccessCount = 0;
        DataSize = 0;
        bIsCompressed = false;
    }
};

/**
 * 地图块信息结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FMapChunk
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Chunk")
    FIntPoint ChunkCoordinate = FIntPoint::ZeroValue; // 块坐标

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Chunk")
    int32 ChunkSize = 32;               // 块大小

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Chunk")
    ELODLevel CurrentLOD = ELODLevel::LOD2_Medium; // 当前LOD级别

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Chunk")
    TArray<FMapCell> CellData;          // 格子数据

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Chunk")
    bool bIsLoaded = false;             // 是否已加载

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Chunk")
    bool bIsDirty = false;              // 是否需要更新

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Chunk")
    float LastUpdateTime = 0.0f;        // 最后更新时间

    FMapChunk()
    {
        ChunkCoordinate = FIntPoint::ZeroValue;
        ChunkSize = 32;
        CurrentLOD = ELODLevel::LOD2_Medium;
        bIsLoaded = false;
        bIsDirty = false;
        LastUpdateTime = 0.0f;
    }
};

/**
 * 性能优化参数结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FPerformanceOptimizationParams
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableObjectPooling = true;   // 是否启用对象池

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableCaching = true;         // 是否启用缓存

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableChunking = true;        // 是否启用分块

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableLOD = true;             // 是否启用LOD

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    int32 MaxCacheSize = 1024;          // 最大缓存大小（MB）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    int32 ChunkSize = 32;               // 地图块大小

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    float LODDistance1 = 100.0f;        // LOD1距离阈值

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    float LODDistance2 = 200.0f;        // LOD2距离阈值

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    float LODDistance3 = 500.0f;        // LOD3距离阈值

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    float UpdateFrequency = 0.1f;       // 更新频率（秒）

    FPerformanceOptimizationParams()
    {
        bEnableObjectPooling = true;
        bEnableCaching = true;
        bEnableChunking = true;
        bEnableLOD = true;
        MaxCacheSize = 1024;
        ChunkSize = 32;
        LODDistance1 = 100.0f;
        LODDistance2 = 200.0f;
        LODDistance3 = 500.0f;
        UpdateFrequency = 0.1f;
    }
};

/**
 * 性能优化器
 * 负责对象池管理、缓存系统、分块加载、LOD等性能优化功能
 */
UCLASS(BlueprintType, Blueprintable)
class GAME_API UPerformanceOptimizer : public UObject
{
    GENERATED_BODY()

public:
    UPerformanceOptimizer();

    // 【现代化升级】RAII析构函数，智能指针自动管理内存
    virtual ~UPerformanceOptimizer() = default;

    // 【移动语义升级】移动构造函数和移动赋值操作符
    UPerformanceOptimizer(UPerformanceOptimizer&& Other) noexcept = default;
    UPerformanceOptimizer& operator=(UPerformanceOptimizer&& Other) noexcept = default;

    // 【现代化】禁用拷贝构造和拷贝赋值（UObject通常不应该被拷贝）
    UPerformanceOptimizer(const UPerformanceOptimizer&) = delete;
    UPerformanceOptimizer& operator=(const UPerformanceOptimizer&) = delete;

    // ========== 主要优化函数 ==========
    
    /**
     * 初始化性能优化器
     * @param Params 优化参数
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void Initialize(const FPerformanceOptimizationParams& Params);

    /**
     * 更新性能优化系统
     * @param DeltaTime 时间增量
     * @param ViewerPosition 观察者位置
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void UpdateOptimization(float DeltaTime, const FVector& ViewerPosition);

    // ========== 对象池管理 ==========
    
    /**
     * 【现代化升级】从对象池获取地图格子 - 使用智能指针
     * @return 地图格子智能指针
     */
    TSharedPtr<FMapCell> GetPooledMapCell();

    /**
     * 【现代化升级】将地图格子返回对象池 - 使用智能指针
     * @param Cell 地图格子智能指针
     */
    void ReturnPooledMapCell(TSharedPtr<FMapCell> Cell);

    // 【已删除】PreallocateObjectPool - 已被PreallocatePool<T>模板函数替代

    /**
     * 【现代化升级】清空对象池 - 智能指针自动管理内存
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void ClearObjectPool();

    // 【已删除】GetObjectPoolSize - 已被GetActiveObjectsCount替代

public:
    // ========== 统一对象池系统 ==========

    /**
     * 【统一对象池】通用对象获取接口
     * @return 对象智能指针
     */
    template<typename T>
    TSharedPtr<T> GetPooledObject();

    /**
     * 【统一对象池】通用对象返回接口
     * @param Object 要返回的对象
     */
    template<typename T>
    void ReturnPooledObject(TSharedPtr<T> Object);

    /**
     * 【统一对象池】通用对象池预分配
     * @param Count 预分配数量
     */
    template<typename T>
    void PreallocatePool(int32 Count);

    /**
     * 【统一对象池】获取池统计信息
     * @return 池命中率
     */
    template<typename T>
    float GetPoolHitRate() const;

    /**
     * 【统一对象池】获取MapCell池命中率 - 蓝图接口
     * @return MapCell池命中率
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Unified Pool")
    float GetMapCellPoolHitRate() const;

    /**
     * 【统一对象池】获取Actor池命中率 - 蓝图接口
     * @return Actor池命中率
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Unified Pool")
    float GetActorPoolHitRate() const;

    /**
     * 【统一对象池】获取池活跃对象数量 - 蓝图接口
     * @return 活跃对象数量
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Unified Pool")
    int32 GetActiveObjectsCount() const;

    /**
     * 【统一对象池】获取池总分配次数 - 蓝图接口
     * @return 总分配次数
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Unified Pool")
    int32 GetTotalAllocationsCount() const;

private:
    // ========== 统一对象池辅助函数 ==========

    /**
     * 【统一对象池】获取指定类型的对象池引用
     */
    template<typename T>
    auto& GetPoolForType();

    template<typename T>
    const auto& GetPoolForType() const;

    /**
     * 【统一对象池】重置对象状态
     */
    template<typename T>
    void ResetObjectState(TSharedPtr<T> Object);

    /**
     * 【移动语义升级】完美转发模板函数 - 高效的容器操作
     * @param Container 目标容器
     * @param Args 要添加的元素参数
     */
    template<typename ContainerType, typename... Args>
    void EmplaceToContainer(ContainerType& Container, Args&&... Arguments)
    {
        Container.Emplace(Forward<Args>(Arguments)...);
    }

    // ========== 缓存系统 ==========
    
    /**
     * 缓存地图数据（拷贝版本）
     * @param Key 缓存键
     * @param Data 数据
     * @param Level 缓存级别
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void CacheMapData(const FString& Key, const TArray<FMapCell>& Data, ECacheLevel Level = ECacheLevel::L1_Memory);

    /**
     * 【移动语义升级】缓存地图数据（移动版本）- 避免大数组拷贝
     * @param Key 缓存键
     * @param Data 地图数据（将被移动）
     * @param Level 缓存级别
     */
    void CacheMapData(const FString& Key, TArray<FMapCell>&& Data, ECacheLevel Level = ECacheLevel::L1_Memory);

    /**
     * 获取缓存的地图数据
     * @param Key 缓存键
     * @param OutData 输出数据
     * @return 是否找到缓存
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    bool GetCachedMapData(const FString& Key, TArray<FMapCell>& OutData);

    /**
     * 清理过期缓存
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void CleanupExpiredCache();

    // ========== 分块系统 ==========
    
    /**
     * 将地图分割为块
     * @param MapData 地图数据
     * @param Width 地图宽度
     * @param Height 地图高度
     * @return 地图块数组
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    TArray<FMapChunk> ChunkifyMap(const TArray<FMapCell>& MapData, int32 Width, int32 Height);

    /**
     * 加载指定块
     * @param ChunkCoordinate 块坐标
     * @param LODLevel LOD级别
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void LoadChunk(const FIntPoint& ChunkCoordinate, ELODLevel LODLevel);

    /**
     * 卸载指定块
     * @param ChunkCoordinate 块坐标
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void UnloadChunk(const FIntPoint& ChunkCoordinate);

    // ========== LOD系统 ==========
    
    /**
     * 根据距离计算LOD级别
     * @param Distance 距离
     * @return LOD级别
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    ELODLevel CalculateLODLevel(float Distance) const;

    /**
     * 应用LOD到地图块
     * @param Chunk 地图块（输入输出）
     * @param TargetLOD 目标LOD级别
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void ApplyLODToChunk(FMapChunk& Chunk, ELODLevel TargetLOD);

    // ========== 性能监控 ==========
    
    /**
     * 获取性能统计信息
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    FPerformanceStats GetPerformanceStats() const;

    /**
     * 重置性能统计
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void ResetPerformanceStats();

    // ========== 默认配置 ==========
    
    /**
     * 获取默认优化参数
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    static FPerformanceOptimizationParams GetDefaultOptimizationParams();

private:
    // ========== 私有成员变量 ==========
    
    UPROPERTY()
    FPerformanceOptimizationParams OptimizationParams;

    // 【统一对象池】通用对象池系统 - 替代多个重复的池
    template<typename T>
    struct FUnifiedObjectPool
    {
        TArray<TSharedPtr<T>> Pool;
        std::atomic<int32> ActiveObjects{0};
        std::atomic<int32> TotalAllocations{0};
        std::atomic<int32> TotalReturns{0};
        FCriticalSection PoolMutex;

        // 池统计信息
        float GetHitRate() const noexcept
        {
            const int32 Total = TotalAllocations.load();
            const int32 Hits = TotalReturns.load();
            return Total > 0 ? static_cast<float>(Hits) / Total : 0.0f;
        }
    };

    // 【统一对象池】各类型对象池
    FUnifiedObjectPool<FMapCell> MapCellPool;
    FUnifiedObjectPool<AActor> ActorPool;  // 替代Mass Actor池

    UPROPERTY()
    TMap<FString, FCacheEntry> CacheEntries; // 缓存条目

    UPROPERTY()
    TMap<FIntPoint, FMapChunk> LoadedChunks; // 已加载的块

    UPROPERTY()
    FPerformanceStats PerformanceStats; // 性能统计

    UPROPERTY()
    float LastUpdateTime = 0.0f;        // 上次更新时间

    // 【升级】新增高精度时间测量
    double LastFrameTime = 0.0;         // 上一帧时间（高精度）

    // 【并行处理升级】并行计算相关成员
    mutable std::atomic<bool> bParallelProcessingEnabled{true};  // 是否启用并行处理
    mutable std::atomic<int32> ParallelTaskCount{0};             // 当前并行任务数量

    // 【SIMD向量化升级】SIMD性能统计
    mutable std::atomic<int64> SIMDOperationsCount{0};           // SIMD操作计数
    mutable std::atomic<int64> ScalarOperationsCount{0};         // 标量操作计数

    // 【已删除】分层内存池成员变量 - 已被统一对象池替代

    // ========== 私有辅助函数 ==========
    
    // 更新性能统计
    void UpdatePerformanceStats();
    
    // 管理内存使用
    void ManageMemoryUsage();
    
    // 压缩缓存数据
    TArray<uint8> CompressData(const TArray<FMapCell>& Data);
    
    // 解压缓存数据
    TArray<FMapCell> DecompressData(const TArray<uint8>& CompressedData);
    
    // 【移动语义升级】简化地图数据（用于LOD）- 统一使用移动版本
    TArray<FMapCell> SimplifyMapData(TArray<FMapCell>&& OriginalData, ELODLevel LODLevel);
    
    // 【现代化升级】计算两点间距离 - constexpr和noexcept优化
    static constexpr float CalculateDistance(const FIntPoint& A, const FIntPoint& B) noexcept;

    // 【现代化升级】获取块索引 - constexpr和noexcept优化
    static constexpr FIntPoint GetChunkCoordinate(int32 X, int32 Y, int32 ChunkSize) noexcept;

public:
    // ========== Mass系统对象池管理 ==========

    /**
     * 从Mass系统对象池获取或创建Actor
     * @param World 世界对象
     * @param ActorClass Actor类型
     * @param Transform Actor变换
     * @param ActorID Actor标识符
     * @return 获取或创建的Actor
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Object Pool")
    AActor* GetOrCreateActorFromPool(UWorld* World, TSubclassOf<AActor> ActorClass, const FTransform& Transform, const FString& ActorID);

    /**
     * 将Actor释放回Mass系统对象池
     * @param World 世界对象
     * @param Actor 要释放的Actor
     * @return 是否成功释放
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Object Pool")
    bool ReleaseActorToPool(UWorld* World, AActor* Actor);

    // 【已删除】Mass Actor池函数 - 已被统一对象池替代

    /**
     * 【现代化升级】检查Actor是否来自对象池 - noexcept优化
     * @param Actor 要检查的Actor
     * @return 是否来自对象池
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Object Pool")
    bool IsActorFromPool(AActor* Actor) const noexcept;

public:
    // ========== 并行处理控制 ==========

    /**
     * 【并行处理升级】启用/禁用并行处理
     * @param bEnabled 是否启用并行处理
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Parallel Processing")
    void SetParallelProcessingEnabled(bool bEnabled) noexcept;

    /**
     * 【并行处理升级】检查并行处理是否启用
     * @return 是否启用并行处理
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Parallel Processing")
    bool IsParallelProcessingEnabled() const noexcept;

    /**
     * 【并行处理升级】获取当前并行任务数量
     * @return 当前并行任务数量
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Parallel Processing")
    int32 GetParallelTaskCount() const noexcept;

public:
    // ========== SIMD向量化优化 ==========

    /**
     * 【SIMD向量化升级】批量距离计算 - 使用SIMD指令
     * @param PointsA 起始点数组
     * @param PointsB 目标点数组
     * @param OutDistances 输出距离数组
     */
    void CalculateDistancesBatch(
        const TArray<FIntPoint>& PointsA,
        const TArray<FIntPoint>& PointsB,
        TArray<float>& OutDistances) noexcept;

    /**
     * 【SIMD向量化升级】批量处理地图格子 - 使用SIMD指令
     * @param MapCells 地图格子数组
     * @param SimplificationFactor 简化因子
     */
    void ProcessMapCellsBatch(TArray<FMapCell>& MapCells, float SimplificationFactor) noexcept;

    /**
     * 【SIMD向量化升级】内存对齐的数据拷贝 - 使用SIMD优化
     * @param Source 源数据
     * @param Destination 目标数据
     */
    void CopyMapCellsAligned(const TArray<FMapCell>& Source, TArray<FMapCell>& Destination) noexcept;

    /**
     * 【SIMD向量化升级】获取SIMD操作统计
     * @return SIMD操作次数
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|SIMD")
    int64 GetSIMDOperationsCount() const noexcept;

    /**
     * 【SIMD向量化升级】获取标量操作统计
     * @return 标量操作次数
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|SIMD")
    int64 GetScalarOperationsCount() const noexcept;

    /**
     * 【SIMD向量化升级】获取SIMD效率比例
     * @return SIMD操作占总操作的比例 (0.0-1.0)
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|SIMD")
    float GetSIMDEfficiencyRatio() const noexcept;

    // 【已删除】分层内存池函数 - 已被统一对象池替代，避免重复实现
};
