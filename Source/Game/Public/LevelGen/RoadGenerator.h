#pragma once

#include "CoreMinimal.h"
#include "MapUtil.h"
#include "Engine/Engine.h"
#include "RoadGenerator.generated.h"

/**
 * 路径节点结构体（用于A*算法）
 */
USTRUCT(BlueprintType)
struct GAME_API FPathNode
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Path Node")
    FIntPoint Position = FIntPoint::ZeroValue;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Path Node")
    float GCost = 0.0f;                 // 从起点到当前节点的实际代价

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Path Node")
    float HCost = 0.0f;                 // 从当前节点到终点的启发式代价

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Path Node")
    float FCost = 0.0f;                 // 总代价 (GCost + HCost)

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Path Node")
    FIntPoint Parent = FIntPoint(-1, -1); // 父节点位置

    FPathNode()
    {
        Position = FIntPoint::ZeroValue;
        GCost = 0.0f;
        HCost = 0.0f;
        FCost = 0.0f;
        Parent = FIntPoint(-1, -1);
    }

    FPathNode(const FIntPoint& InPosition)
    {
        Position = InPosition;
        GCost = 0.0f;
        HCost = 0.0f;
        FCost = 0.0f;
        Parent = FIntPoint(-1, -1);
    }

    void CalculateFCost()
    {
        FCost = GCost + HCost;
    }
};

/**
 * 道路段信息结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FRoadSegment
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Road Segment")
    FIntPoint StartPoint = FIntPoint::ZeroValue;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Road Segment")
    FIntPoint EndPoint = FIntPoint::ZeroValue;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Road Segment")
    ERoadType RoadType = ERoadType::None;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Road Segment")
    TArray<FIntPoint> PathPoints;       // 路径上的所有点

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Road Segment")
    float TotalCost = 0.0f;             // 路径总代价

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Road Segment")
    float TrafficLevel = 0.0f;          // 交通流量 (0.0-1.0)

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Road Segment")
    int32 Age = 0;                      // 道路年代

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Road Segment")
    bool bIsActive = true;              // 是否激活使用

    FRoadSegment()
    {
        StartPoint = FIntPoint::ZeroValue;
        EndPoint = FIntPoint::ZeroValue;
        RoadType = ERoadType::None;
        TotalCost = 0.0f;
        TrafficLevel = 0.0f;
        Age = 0;
        bIsActive = true;
    }
};

/**
 * 道路网络结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FRoadNetwork
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Road Network")
    TArray<FRoadSegment> RoadSegments;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Road Network")
    TArray<FIntPoint> ImportantNodes;   // 重要节点（城市、资源点等）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Road Network")
    float NetworkDensity = 0.0f;        // 网络密度

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Road Network")
    float AverageTraffic = 0.0f;        // 平均交通流量

    FRoadNetwork()
    {
        NetworkDensity = 0.0f;
        AverageTraffic = 0.0f;
    }
};

/**
 * 道路生成参数
 */
USTRUCT(BlueprintType)
struct GAME_API FRoadGenerationParams
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    float MaxRoadDensity = 0.3f;        // 最大道路密度

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    float MinConnectionDistance = 10.0f; // 最小连接距离

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    float MaxConnectionDistance = 50.0f; // 最大连接距离

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    bool bConnectFactionCenters = true; // 是否连接派系中心

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    bool bConnectResourceNodes = true;  // 是否连接资源节点

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    bool bConnectHistoricalSites = true; // 是否连接历史遗迹

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    float ResistanceWeight = 1.0f;      // 阻力权重

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    float DistanceWeight = 0.5f;        // 距离权重

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    bool bAllowUndergroundPaths = true; // 是否允许地下通道

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    bool bEnableDynamicUpdates = true;  // 是否启用动态更新

    FRoadGenerationParams()
    {
        MaxRoadDensity = 0.3f;
        MinConnectionDistance = 10.0f;
        MaxConnectionDistance = 50.0f;
        bConnectFactionCenters = true;
        bConnectResourceNodes = true;
        bConnectHistoricalSites = true;
        ResistanceWeight = 1.0f;
        DistanceWeight = 0.5f;
        bAllowUndergroundPaths = true;
        bEnableDynamicUpdates = true;
    }
};

/**
 * 道路生成器
 * 负责基于阻力计算生成道路网络
 */
UCLASS(BlueprintType, Blueprintable)
class GAME_API URoadGenerator : public UObject
{
    GENERATED_BODY()

public:
    URoadGenerator();

    // ========== 主要生成函数 ==========
    
    /**
     * 生成完整的道路网络
     * @param MapCells 地图格子数组
     * @param Width 地图宽度
     * @param Height 地图高度
     * @param Params 生成参数
     * @return 生成的道路网络
     */
    UFUNCTION(BlueprintCallable, Category = "Road Generator")
    FRoadNetwork GenerateRoadNetwork(const TArray<FMapCell>& MapCells, int32 Width, int32 Height, 
                                    const FRoadGenerationParams& Params);

    /**
     * 在两点间寻找最优路径
     * @param MapCells 地图格子数组
     * @param Width 地图宽度
     * @param Height 地图高度
     * @param StartPoint 起点
     * @param EndPoint 终点
     * @param Params 生成参数
     * @return 路径点数组
     */
    UFUNCTION(BlueprintCallable, Category = "Road Generator")
    TArray<FIntPoint> FindOptimalPath(const TArray<FMapCell>& MapCells, int32 Width, int32 Height,
                                     const FIntPoint& StartPoint, const FIntPoint& EndPoint,
                                     const FRoadGenerationParams& Params);

    // ========== 阻力计算函数 ==========
    
    /**
     * 计算格子的移动阻力
     * @param Cell 地图格子
     * @param MovementDirection 移动方向
     * @param Params 生成参数
     * @return 阻力值 (0.0-1.0)
     */
    UFUNCTION(BlueprintCallable, Category = "Road Generator")
    float CalculateMovementResistance(const FMapCell& Cell, const FVector2D& MovementDirection,
                                     const FRoadGenerationParams& Params);

    /**
     * 计算两个相邻格子间的移动代价
     * @param FromCell 起始格子
     * @param ToCell 目标格子
     * @param MovementDirection 移动方向
     * @param Params 生成参数
     * @return 移动代价
     */
    UFUNCTION(BlueprintCallable, Category = "Road Generator")
    float CalculateMovementCost(const FMapCell& FromCell, const FMapCell& ToCell,
                               const FVector2D& MovementDirection, const FRoadGenerationParams& Params);

    // ========== 道路类型确定函数 ==========
    
    /**
     * 根据环境确定道路类型
     * @param PathPoints 路径点数组
     * @param MapCells 地图格子数组
     * @param Width 地图宽度
     * @param Height 地图高度
     * @return 道路类型
     */
    UFUNCTION(BlueprintCallable, Category = "Road Generator")
    ERoadType DetermineRoadType(const TArray<FIntPoint>& PathPoints, const TArray<FMapCell>& MapCells,
                               int32 Width, int32 Height);

    // ========== 重要节点识别函数 ==========
    
    /**
     * 识别地图上的重要节点
     * @param MapCells 地图格子数组
     * @param Width 地图宽度
     * @param Height 地图高度
     * @param Params 生成参数
     * @return 重要节点位置数组
     */
    UFUNCTION(BlueprintCallable, Category = "Road Generator")
    TArray<FIntPoint> IdentifyImportantNodes(const TArray<FMapCell>& MapCells, int32 Width, int32 Height,
                                            const FRoadGenerationParams& Params);

    // ========== 网络优化函数 ==========
    
    /**
     * 优化道路网络（移除冗余路径、合并相似路径等）
     * @param RoadNetwork 道路网络
     * @param MapCells 地图格子数组
     * @param Width 地图宽度
     * @param Height 地图高度
     */
    UFUNCTION(BlueprintCallable, Category = "Road Generator")
    void OptimizeRoadNetwork(FRoadNetwork& RoadNetwork, const TArray<FMapCell>& MapCells,
                            int32 Width, int32 Height);

    // ========== 动态更新函数 ==========
    
    /**
     * 更新道路网络（响应地图变化）
     * @param RoadNetwork 道路网络
     * @param MapCells 地图格子数组
     * @param Width 地图宽度
     * @param Height 地图高度
     * @param ChangedAreas 变化区域
     * @param Params 生成参数
     */
    UFUNCTION(BlueprintCallable, Category = "Road Generator")
    void UpdateRoadNetwork(FRoadNetwork& RoadNetwork, const TArray<FMapCell>& MapCells,
                          int32 Width, int32 Height, const TArray<FIntPoint>& ChangedAreas,
                          const FRoadGenerationParams& Params);

    // ========== 默认配置获取函数 ==========
    
    /**
     * 获取默认道路生成参数
     */
    UFUNCTION(BlueprintCallable, Category = "Road Generator")
    static FRoadGenerationParams GetDefaultRoadParams();

private:
    // ========== A*算法相关私有函数 ==========
    
    // A*路径搜索实现
    TArray<FIntPoint> AStar(const TArray<FMapCell>& MapCells, int32 Width, int32 Height,
                           const FIntPoint& Start, const FIntPoint& Goal, const FRoadGenerationParams& Params);
    
    // 计算启发式距离
    float CalculateHeuristic(const FIntPoint& A, const FIntPoint& B);
    
    // 重构路径
    TArray<FIntPoint> ReconstructPath(const TMap<FIntPoint, FIntPoint>& CameFrom, const FIntPoint& Current);
    
    // 获取邻居节点
    TArray<FIntPoint> GetNeighbors(const FIntPoint& Node, int32 Width, int32 Height);
    
    // ========== 道路网络分析私有函数 ==========
    
    // 计算网络连通性
    float CalculateNetworkConnectivity(const FRoadNetwork& Network);
    
    // 检测冗余路径
    TArray<int32> DetectRedundantPaths(const FRoadNetwork& Network);
    
    // 合并相似路径
    void MergeSimilarPaths(FRoadNetwork& Network);
    
    // ========== 工具函数 ==========
    
    // 获取格子索引
    static int32 GetCellIndex(int32 X, int32 Y, int32 Width);
    
    // 检查坐标有效性
    static bool IsValidCoordinate(int32 X, int32 Y, int32 Width, int32 Height);
    
    // 计算两点间距离
    static float CalculateDistance(const FIntPoint& A, const FIntPoint& B);
};
