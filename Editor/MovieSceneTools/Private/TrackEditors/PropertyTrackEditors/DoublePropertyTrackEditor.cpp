// Copyright Epic Games, Inc. All Rights Reserved.

#include "TrackEditors/PropertyTrackEditors/DoublePropertyTrackEditor.h"
#include "Editor/UnrealEdEngine.h"
#include "UnrealEdGlobals.h"
#include "MovieSceneToolHelpers.h"
#include "Evaluation/MovieScenePropertyTemplate.h"

#include "Systems/MovieScenePropertyInstantiator.h"
#include "MovieSceneTracksComponentTypes.h"

#define LOCTEXT_NAMESPACE "DoublePropertyTrackEditor"

TSharedRef<ISequencerTrackEditor> FDoublePropertyTrackEditor::CreateTrackEditor( TSharedRef<ISequencer> OwningSequencer )
{
	return MakeShareable(new FDoublePropertyTrackEditor(OwningSequencer));
}

FText FDoublePropertyTrackEditor::GetDisplayName() const
{
	return LOCTEXT("DoublePropertyTrackEditor_DisplayName", "Double Property");
}

void FDoublePropertyTrackEditor::GenerateKeysFromPropertyChanged( const FPropertyChangedParams& PropertyChangedParams, UMovieSceneSection* SectionToKey, FGeneratedTrackKeys& OutGeneratedKeys )
{
	const double KeyedValue = PropertyChangedParams.GetPropertyValue<double>();
	const double NewValue   = RecomposeDouble(KeyedValue, PropertyChangedParams.ObjectsThatChanged[0], SectionToKey);
	OutGeneratedKeys.Add(FMovieSceneChannelValueSetter::Create<FMovieSceneDoubleChannel>(0, NewValue, true));
}


double FDoublePropertyTrackEditor::RecomposeDouble(double InCurrentValue, UObject* AnimatedObject, UMovieSceneSection* Section)
{
	using namespace UE::MovieScene;

	double Recomposed = InCurrentValue;

	const FMovieSceneRootEvaluationTemplateInstance& EvaluationTemplate = GetSequencer()->GetEvaluationTemplate();

	UMovieSceneEntitySystemLinker* EntityLinker = EvaluationTemplate.GetEntitySystemLinker();
	FMovieSceneEntityID EntityID = EvaluationTemplate.FindEntityFromOwner(Section, 0, GetSequencer()->GetFocusedTemplateID());

	if (EntityLinker && EntityID)
	{
		TGuardValue<FEntityManager*> DebugVizGuard(GEntityManagerForDebuggingVisualizers, &EntityLinker->EntityManager);

		UMovieScenePropertyInstantiatorSystem* System = EntityLinker->FindSystem<UMovieScenePropertyInstantiatorSystem>();
		if (System)
		{
			FDecompositionQuery Query;
			Query.Entities = MakeArrayView(&EntityID, 1);
			Query.Object   = AnimatedObject;

			Recomposed = System->RecomposeBlend(FMovieSceneTracksComponentTypes::Get()->Double, Query, InCurrentValue).Values[0];
		}
	}

	return Recomposed;
}

#undef LOCTEXT_NAMESPACE
