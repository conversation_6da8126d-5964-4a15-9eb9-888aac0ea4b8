TPS
"<?xml version=""1.0"" encoding=""utf-8""?>
<TpsData xmlns:xsd=""http://www.w3.org/2001/XMLSchema"" xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"">
  <Name>Simde	</Name>
  <!-- Software Name and Version  -->
<!-- Software Name: Simde	
    Download Link: https://github.com/simd-everywhere/simde/blob/master/COPYING
    Version: 0.7.7
    Notes: This license had been approved previously, but we’re currently unable to locate a record of that approval. The team members involved in the original process are no longer with the company, which makes verification difficult.

For clarity, the license in question is being used by MetaHuman within Unreal Engine, and not for MHC in the cloud.
Reference license URL: https://github.com/simd-everywhere/simde/blob/master/COPYING

Current location:
- \\Fortnite\Main\Engine\Source\ThirdParty\simde\simde.tps

        -->
<Location>\\Fortnite\Main\Engine\Source\ThirdParty\simde</Location>
<Function>The library consists of a source code that is compiled into the MH Plugin</Function>
<Eula>https://github.com/simd-everywhere/simde/blob/master/COPYING</Eula>
  <RedistributeTo>
<EndUserGroup></EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>
 "
