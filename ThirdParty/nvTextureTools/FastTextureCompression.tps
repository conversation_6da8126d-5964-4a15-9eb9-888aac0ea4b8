<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Fast Texture Compression</Name>
  <Location>/Engine/Source/ThirdParty/nvTextureTools/</Location>
  <Date>2016-06-09T17:18:44.8747018-04:00</Date>
  <Function>Compresses textures to BC7</Function>
  <Justification> BC7 is a much higher quality texture compression method available for DX11 class GPUs.</Justification>
  <Eula>See installer EULA: https://software.intel.com/en-us/articles/fast-texture-compression-sample</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>None</LicenseFolder>
</TpsData>