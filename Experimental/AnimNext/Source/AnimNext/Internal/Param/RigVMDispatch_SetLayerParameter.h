// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "RigVMCore/RigVMDispatchFactory.h"
#include "AnimNextExecuteContext.h"
#include "RigVMDispatch_SetLayerParameter.generated.h"

#define UE_API ANIMNEXT_API

namespace UE::AnimNext::UncookedOnly
{
struct FUtils;
}

/*
 * Sets a parameter's value
 */
USTRUCT(meta=(Deprecated, DisplayName = "Set Graph Parameter", Category="Parameters", NodeColor = "0.8, 0, 0.2, 1"))
struct FRigVMDispatch_SetLayerParameter : public FRigVMDispatchFactory
{
	GENERATED_BODY()

	UE_API FRigVMDispatch_SetLayerParameter();

private:
	friend struct UE::AnimNext::UncookedOnly::FUtils;

	virtual UScriptStruct* GetExecuteContextStruct() const { return FAnimNextExecuteContext::StaticStruct(); }
	UE_API virtual FName GetArgumentNameForOperandIndex(int32 InOperandIndex, int32 InTotalOperands) const override;
	UE_API virtual const TArray<FRigVMTemplateArgumentInfo>& GetArgumentInfos() const override;
#if WITH_EDITOR
	UE_API virtual FString GetArgumentMetaData(const FName& InArgumentName, const FName& InMetaDataKey) const override;
#endif
	UE_API virtual TArray<FRigVMExecuteArgument>& GetExecuteArguments_Impl(const FRigVMDispatchContext& InContext) const override;
	UE_API virtual FRigVMTemplateTypeMap OnNewArgumentType(const FName& InArgumentName, TRigVMTypeIndex InTypeIndex) const override;
	virtual bool IsSingleton() const override { return true; } 

	virtual FRigVMFunctionPtr GetDispatchFunctionImpl(const FRigVMTemplateTypeMap& InTypes) const override
	{
		return &FRigVMDispatch_SetLayerParameter::Execute;
	}
	static UE_API void Execute(FRigVMExtendedExecuteContext& InContext, FRigVMMemoryHandleArray Handles, FRigVMPredicateBranchArray RigVMBranches);

	static UE_API const FName ExecuteContextName;
	static UE_API const FName ParameterName;
	static UE_API const FName ParameterIdName;
	static UE_API const FName TypeHandleName;
	static UE_API const FName ValueName;
};

#undef UE_API
