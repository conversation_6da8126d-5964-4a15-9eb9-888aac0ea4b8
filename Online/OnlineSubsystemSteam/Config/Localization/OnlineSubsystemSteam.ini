[CommonSettings]
SourcePath=Plugins/Online/OnlineSubsystemSteam/Content/Localization/OnlineSubsystemSteam
DestinationPath=Plugins/Online/OnlineSubsystemSteam/Content/Localization/OnlineSubsystemSteam
ManifestName=OnlineSubsystemSteam.manifest
ArchiveName=OnlineSubsystemSteam.archive
PortableObjectName=OnlineSubsystemSteam.po
ResourceName=OnlineSubsystemSteam.locres
NativeCulture=en
CulturesToGenerate=en
CulturesToGenerate=fr
CulturesToGenerate=de
CulturesToGenerate=es
CulturesToGenerate=es-419
CulturesToGenerate=it
CulturesToGenerate=ja
CulturesToGenerate=ko
CulturesToGenerate=ru
CulturesToGenerate=zh-Hans
CulturesToGenerate=ar
CulturesToGenerate=pl
CulturesToGenerate=pt-BR

;Gather text from source code
[GatherTextStep0]
CommandletClass=GatherTextFromSource
SearchDirectoryPaths=Plugins/Online/OnlineSubsystemSteam/Source/
FileNameFilters=*.cpp
FileNameFilters=*.h
FileNameFilters=*.c
FileNameFilters=*.inl
FileNameFilters=*.mm
FileNameFilters=*.ini
ShouldGatherFromEditorOnlyData=false

;Write Manifest
[GatherTextStep1]
CommandletClass=GenerateGatherManifest

;Write Archives
[GatherTextStep2]
CommandletClass=GenerateGatherArchive

;Import localized PO files
[GatherTextStep3]
CommandletClass=InternationalizationExport
bImportLoc=true

;Write Localized Text Resource
[GatherTextStep4]
CommandletClass=GenerateTextLocalizationResource

;Export  PO files
[GatherTextStep5]
CommandletClass=InternationalizationExport
bExportLoc=true