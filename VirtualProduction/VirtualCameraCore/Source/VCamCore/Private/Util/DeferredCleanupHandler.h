// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "Containers/Array.h"
#include "UObject/WeakObjectPtr.h"
#include "UObject/WeakObjectPtrTemplates.h"
#include "Templates/UnrealTemplate.h"

class UVCamComponent;

namespace UE::VCamCore
{
	/**
	 * This class is responsible for cleaning up any resources created by UVCamComponents that implement the FDeferredCleanupInterface.
	 *
	 * These resources are typically generated by user code, such as widgets, output providers, or modifiers.
	 * A common example of such a resource is a dynamic material instance used within a UUserWidget wrapped by SRetainerWidget.
	 * Without cleanup, the FRetainerWidgetRenderingResources within SRetainerWidget::RenderingResources continues referencing the material until the
	 * rendering thread cleans up the resources. When the editor world is destroyed, this leads to a fatal error during UEditorEngine::CheckForWorldGCLeaks.
	 *
	 * To prevent this, all UVCamComponents are deinitialized when FEditorSupportDelegates::PrepareToCleanseEditorObject is invoked, 
	 * and any pending rendering commands are flushed when FEditorSupportDelegates::CleanseEditor is called.
	 */
	class FDeferredCleanupHandler : public FNoncopyable
	{
	public:
		
		FDeferredCleanupHandler();
		~FDeferredCleanupHandler();

		/** Starts tracking Component so its rendering resources are destroyed correctly. */
		void OnInitializeVCam(UVCamComponent& Component);
		/** Cleans up Component's rendering resources if needed and stops tracking it for clean-up. */
		void OnDeinitializeVCam(UVCamComponent& Component);

	private:

#if WITH_EDITOR

		/** The VCams that may need cleaning up. */
		TArray<TWeakObjectPtr<UVCamComponent>> KnownVCams;

		/** Whether a flush of rendering resources should be triggered to ensure that any deferred rendering resources are cleaned up. */
		bool bNeedsFlush = false;

		/** Delegate into FEditorSupportDelegates::PrepareToCleanseEditorObject. */
		void OnPrepareToCleanseEditor(UObject* Object);
		/** Delegate into FEditorSupportDelegates::CleanseEditor. */
		void OnCleanseEditor();
#endif
	};
}

