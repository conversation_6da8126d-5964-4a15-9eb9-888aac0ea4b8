[CoreRedirects]
+ClassRedirects=(OldName="/Script/VCamCore.VCamCoreSubsystem",NewName="/Script/VCamInput.VCamInputSubsystem")
+ClassRedirects=(OldName="/Script/VirtualCamera.RadialSlider",NewName="/Script/AdvancedWidgets.RadialSlider")
+FunctionRedirects=(OldName="VCamComponent.ClearModifiers",NewName="VCamComponent.RemoveAllModifiers")
+FunctionRedirects=(OldName="VCamComponent.FindModifierByName",NewName="VCamComponent.GetModifierByName")
+FunctionRedirects=(OldName="VCamComponent.FindModifiersByClass",NewName="VCamComponent.GetModifiersByClass")
+FunctionRedirects=(OldName="VCamComponent.FindModifierByInterface",NewName="VCamComponent.GetModifiersByInterface")

; 5.2 > 5.3
+ClassRedirects=(OldName="/Script/PixelStreamingVCam.VCamPixelStreamingSession",NewName="/Script/DecoupledOutputProvider.VCamPixelStreamingSession")

; 5.3 > 5.4
+ClassRedirects=(OldName="/Script/VCamInput.VCamInputSettings",NewName="/Script/VCamCore.VCamInputSettings")
