//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.Runtime.Serialization;

[assembly: System.Runtime.Serialization.ContractNamespaceAttribute("urn:XLoc.Xml.Types", ClrNamespace="XLoc.Xml.Types")]
[assembly: System.Runtime.Serialization.ContractNamespaceAttribute("", ClrNamespace="")]

namespace XLoc.Xml.Types
{
    using System.Runtime.Serialization;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Version", Namespace="urn:XLoc.Xml.Types")]
    public partial class Version : object, System.Runtime.Serialization.IExtensibleDataObject
    {
        
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private System.Guid LocalizationIdField;
        
        private System.Guid VersionIdField;
        
        private string VersionNameField;
        
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData
        {
            get
            {
                return this.extensionDataField;
            }
            set
            {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Guid LocalizationId
        {
            get
            {
                return this.LocalizationIdField;
            }
            set
            {
                this.LocalizationIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public System.Guid VersionId
        {
            get
            {
                return this.VersionIdField;
            }
            set
            {
                this.VersionIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public string VersionName
        {
            get
            {
                return this.VersionNameField;
            }
            set
            {
                this.VersionNameField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Language", Namespace="urn:XLoc.Xml.Types")]
    public partial class Language : object, System.Runtime.Serialization.IExtensibleDataObject
    {
        
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private string LanguageIdField;
        
        private string LanguageNameField;
        
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData
        {
            get
            {
                return this.extensionDataField;
            }
            set
            {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public string LanguageId
        {
            get
            {
                return this.LanguageIdField;
            }
            set
            {
                this.LanguageIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public string LanguageName
        {
            get
            {
                return this.LanguageNameField;
            }
            set
            {
                this.LanguageNameField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Session", Namespace="urn:XLoc.Xml.Types")]
    public partial class Session : object, System.Runtime.Serialization.IExtensibleDataObject
    {
        
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private System.Guid AuthTokenField;
        
        private XLoc.Xml.Types.UserAccount UserField;
        
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData
        {
            get
            {
                return this.extensionDataField;
            }
            set
            {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Guid AuthToken
        {
            get
            {
                return this.AuthTokenField;
            }
            set
            {
                this.AuthTokenField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public XLoc.Xml.Types.UserAccount User
        {
            get
            {
                return this.UserField;
            }
            set
            {
                this.UserField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UserAccount", Namespace="urn:XLoc.Xml.Types")]
    public partial class UserAccount : object, System.Runtime.Serialization.IExtensibleDataObject
    {
        
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private string EmailField;
        
        private string LanguageField;
        
        private System.Guid LocalizationIdField;
        
        private string NameField;
        
        private string RoleField;
        
        private int TimeZoneField;
        
        private System.Guid UserIdField;
        
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData
        {
            get
            {
                return this.extensionDataField;
            }
            set
            {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Email
        {
            get
            {
                return this.EmailField;
            }
            set
            {
                this.EmailField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Language
        {
            get
            {
                return this.LanguageField;
            }
            set
            {
                this.LanguageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Guid LocalizationId
        {
            get
            {
                return this.LocalizationIdField;
            }
            set
            {
                this.LocalizationIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Role
        {
            get
            {
                return this.RoleField;
            }
            set
            {
                this.RoleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TimeZone
        {
            get
            {
                return this.TimeZoneField;
            }
            set
            {
                this.TimeZoneField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Guid UserId
        {
            get
            {
                return this.UserIdField;
            }
            set
            {
                this.UserIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Localization", Namespace="urn:XLoc.Xml.Types")]
    public partial class Localization : object, System.Runtime.Serialization.IExtensibleDataObject
    {
        
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private string DescriptionField;
        
        private System.Guid LocalizationIdField;
        
        private string NameField;
        
        private string StatusField;
        
        private XLoc.Xml.Types.LocalizationType TypeField;
        
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData
        {
            get
            {
                return this.extensionDataField;
            }
            set
            {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Description
        {
            get
            {
                return this.DescriptionField;
            }
            set
            {
                this.DescriptionField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public System.Guid LocalizationId
        {
            get
            {
                return this.LocalizationIdField;
            }
            set
            {
                this.LocalizationIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Status
        {
            get
            {
                return this.StatusField;
            }
            set
            {
                this.StatusField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public XLoc.Xml.Types.LocalizationType Type
        {
            get
            {
                return this.TypeField;
            }
            set
            {
                this.TypeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="LocalizationType", Namespace="urn:XLoc.Xml.Types")]
    public partial class LocalizationType : object, System.Runtime.Serialization.IExtensibleDataObject
    {
        
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private string DescriptionField;
        
        private string NameField;
        
        private System.Guid TypeIdField;
        
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData
        {
            get
            {
                return this.extensionDataField;
            }
            set
            {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Description
        {
            get
            {
                return this.DescriptionField;
            }
            set
            {
                this.DescriptionField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public System.Guid TypeId
        {
            get
            {
                return this.TypeIdField;
            }
            set
            {
                this.TypeIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SearchRequest", Namespace="urn:XLoc.Xml.Types")]
    public partial class SearchRequest : object, System.Runtime.Serialization.IExtensibleDataObject
    {
        
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private string AuthTokenField;
        
        private bool CaseSensitiveField;
        
        private string FileField;
        
        private bool FilterByPlatformField;
        
        private XLoc.Xml.Types.FileGroup[] GroupField;
        
        private bool IncludeContextField;
        
        private string LanguageIdField;
        
        private System.Guid LocalizationIdField;
        
        private int MaxResultsField;
        
        private XLoc.Xml.Types.OrderBy OrderByField;
        
        private string PlatformField;
        
        private bool SearchEnglishField;
        
        private bool SearchRSIDField;
        
        private string SearchStringField;
        
        private bool SearchTranslationsField;
        
        private int StartRowField;
        
        private bool WildcardSearchField;
        
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData
        {
            get
            {
                return this.extensionDataField;
            }
            set
            {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public string AuthToken
        {
            get
            {
                return this.AuthTokenField;
            }
            set
            {
                this.AuthTokenField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool CaseSensitive
        {
            get
            {
                return this.CaseSensitiveField;
            }
            set
            {
                this.CaseSensitiveField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string File
        {
            get
            {
                return this.FileField;
            }
            set
            {
                this.FileField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool FilterByPlatform
        {
            get
            {
                return this.FilterByPlatformField;
            }
            set
            {
                this.FilterByPlatformField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public XLoc.Xml.Types.FileGroup[] Group
        {
            get
            {
                return this.GroupField;
            }
            set
            {
                this.GroupField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IncludeContext
        {
            get
            {
                return this.IncludeContextField;
            }
            set
            {
                this.IncludeContextField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public string LanguageId
        {
            get
            {
                return this.LanguageIdField;
            }
            set
            {
                this.LanguageIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Guid LocalizationId
        {
            get
            {
                return this.LocalizationIdField;
            }
            set
            {
                this.LocalizationIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MaxResults
        {
            get
            {
                return this.MaxResultsField;
            }
            set
            {
                this.MaxResultsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public XLoc.Xml.Types.OrderBy OrderBy
        {
            get
            {
                return this.OrderByField;
            }
            set
            {
                this.OrderByField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Platform
        {
            get
            {
                return this.PlatformField;
            }
            set
            {
                this.PlatformField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SearchEnglish
        {
            get
            {
                return this.SearchEnglishField;
            }
            set
            {
                this.SearchEnglishField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SearchRSID
        {
            get
            {
                return this.SearchRSIDField;
            }
            set
            {
                this.SearchRSIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public string SearchString
        {
            get
            {
                return this.SearchStringField;
            }
            set
            {
                this.SearchStringField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SearchTranslations
        {
            get
            {
                return this.SearchTranslationsField;
            }
            set
            {
                this.SearchTranslationsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int StartRow
        {
            get
            {
                return this.StartRowField;
            }
            set
            {
                this.StartRowField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool WildcardSearch
        {
            get
            {
                return this.WildcardSearchField;
            }
            set
            {
                this.WildcardSearchField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="FileGroup", Namespace="urn:XLoc.Xml.Types")]
    public partial class FileGroup : XLoc.Xml.Types.ActiveMember
    {
        
        private string DescriptionField;
        
        private System.Guid FileGroupIdField;
        
        private XLoc.Xml.Types.Localization LocalizationField;
        
        private string NameField;
        
        private bool SelectedField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Description
        {
            get
            {
                return this.DescriptionField;
            }
            set
            {
                this.DescriptionField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public System.Guid FileGroupId
        {
            get
            {
                return this.FileGroupIdField;
            }
            set
            {
                this.FileGroupIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public XLoc.Xml.Types.Localization Localization
        {
            get
            {
                return this.LocalizationField;
            }
            set
            {
                this.LocalizationField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Selected
        {
            get
            {
                return this.SelectedField;
            }
            set
            {
                this.SelectedField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="OrderBy", Namespace="urn:XLoc.Xml.Types")]
    public enum OrderBy : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Normal = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Unfinished = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        LastChanged = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        LastModifiedSource = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        HighPriority = 4,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UnrevisedStrings = 5,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        StringNumber = 6,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ActiveMember", Namespace="urn:XLoc.Xml.Types")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.GameFile))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.FileGroup))]
    public partial class ActiveMember : object, System.Runtime.Serialization.IExtensibleDataObject
    {
        
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private bool IsActiveField;
        
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData
        {
            get
            {
                return this.extensionDataField;
            }
            set
            {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsActive
        {
            get
            {
                return this.IsActiveField;
            }
            set
            {
                this.IsActiveField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GameFile", Namespace="urn:XLoc.Xml.Types")]
    public partial class GameFile : XLoc.Xml.Types.ActiveMember
    {
        
        private System.Guid GameFileIdField;
        
        private XLoc.Xml.Types.Localization LocalizationField;
        
        private string NameField;
        
        private string NotesField;
        
        private XLoc.Xml.Types.Platform PlatformField;
        
        private string ResourcePrefixField;
        
        private int WordCountField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public System.Guid GameFileId
        {
            get
            {
                return this.GameFileIdField;
            }
            set
            {
                this.GameFileIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public XLoc.Xml.Types.Localization Localization
        {
            get
            {
                return this.LocalizationField;
            }
            set
            {
                this.LocalizationField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Notes
        {
            get
            {
                return this.NotesField;
            }
            set
            {
                this.NotesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public XLoc.Xml.Types.Platform Platform
        {
            get
            {
                return this.PlatformField;
            }
            set
            {
                this.PlatformField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ResourcePrefix
        {
            get
            {
                return this.ResourcePrefixField;
            }
            set
            {
                this.ResourcePrefixField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int WordCount
        {
            get
            {
                return this.WordCountField;
            }
            set
            {
                this.WordCountField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Platform", Namespace="urn:XLoc.Xml.Types")]
    public partial class Platform : object, System.Runtime.Serialization.IExtensibleDataObject
    {
        
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private string DescriptionField;
        
        private XLoc.Xml.Types.Localization[] LocalizationsField;
        
        private System.Guid PlatformIdField;
        
        private string ReferenceField;
        
        private string TitleField;
        
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData
        {
            get
            {
                return this.extensionDataField;
            }
            set
            {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Description
        {
            get
            {
                return this.DescriptionField;
            }
            set
            {
                this.DescriptionField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public XLoc.Xml.Types.Localization[] Localizations
        {
            get
            {
                return this.LocalizationsField;
            }
            set
            {
                this.LocalizationsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public System.Guid PlatformId
        {
            get
            {
                return this.PlatformIdField;
            }
            set
            {
                this.PlatformIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public string Reference
        {
            get
            {
                return this.ReferenceField;
            }
            set
            {
                this.ReferenceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public string Title
        {
            get
            {
                return this.TitleField;
            }
            set
            {
                this.TitleField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SearchResult", Namespace="urn:XLoc.Xml.Types")]
    public partial class SearchResult : object, System.Runtime.Serialization.IExtensibleDataObject
    {
        
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private ContextListData[] ContextField;
        
        private int RecordCountField;
        
        private XLoc.Xml.Types.LocalizedString[] StringsField;
        
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData
        {
            get
            {
                return this.extensionDataField;
            }
            set
            {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public ContextListData[] Context
        {
            get
            {
                return this.ContextField;
            }
            set
            {
                this.ContextField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int RecordCount
        {
            get
            {
                return this.RecordCountField;
            }
            set
            {
                this.RecordCountField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public XLoc.Xml.Types.LocalizedString[] Strings
        {
            get
            {
                return this.StringsField;
            }
            set
            {
                this.StringsField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="LocalizedString", Namespace="urn:XLoc.Xml.Types")]
    public partial class LocalizedString : XLoc.Xml.Types.BaseContract
    {
        
        private string EditLinkField;
        
        private System.DateTime EnglishLastModifiedField;
        
        private string EnglishStringField;
        
        private string FileGroupNameField;
        
        private string LanguageField;
        
        private string LocalizationField;
        
        private System.Guid LocalizationIdField;
        
        private string LocalizedStringMemberField;
        
        private string PlatformTitleField;
        
        private string PriorityField;
        
        private int ResourceIdField;
        
        private string ResourceStringIdField;
        
        private string ResourceStringNumberField;
        
        private string StringIdField;
        
        private System.DateTime TranslationLastModifiedField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EditLink
        {
            get
            {
                return this.EditLinkField;
            }
            set
            {
                this.EditLinkField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public System.DateTime EnglishLastModified
        {
            get
            {
                return this.EnglishLastModifiedField;
            }
            set
            {
                this.EnglishLastModifiedField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public string EnglishString
        {
            get
            {
                return this.EnglishStringField;
            }
            set
            {
                this.EnglishStringField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FileGroupName
        {
            get
            {
                return this.FileGroupNameField;
            }
            set
            {
                this.FileGroupNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public string Language
        {
            get
            {
                return this.LanguageField;
            }
            set
            {
                this.LanguageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public string Localization
        {
            get
            {
                return this.LocalizationField;
            }
            set
            {
                this.LocalizationField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public System.Guid LocalizationId
        {
            get
            {
                return this.LocalizationIdField;
            }
            set
            {
                this.LocalizationIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(Name="LocalizedString", IsRequired=true)]
        public string LocalizedStringMember
        {
            get
            {
                return this.LocalizedStringMemberField;
            }
            set
            {
                this.LocalizedStringMemberField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public string PlatformTitle
        {
            get
            {
                return this.PlatformTitleField;
            }
            set
            {
                this.PlatformTitleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public string Priority
        {
            get
            {
                return this.PriorityField;
            }
            set
            {
                this.PriorityField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int ResourceId
        {
            get
            {
                return this.ResourceIdField;
            }
            set
            {
                this.ResourceIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public string ResourceStringId
        {
            get
            {
                return this.ResourceStringIdField;
            }
            set
            {
                this.ResourceStringIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public string ResourceStringNumber
        {
            get
            {
                return this.ResourceStringNumberField;
            }
            set
            {
                this.ResourceStringNumberField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public string StringId
        {
            get
            {
                return this.StringIdField;
            }
            set
            {
                this.StringIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public System.DateTime TranslationLastModified
        {
            get
            {
                return this.TranslationLastModifiedField;
            }
            set
            {
                this.TranslationLastModifiedField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SessionFault", Namespace="urn:XLoc.Xml.Types")]
    public partial class SessionFault : object, System.Runtime.Serialization.IExtensibleDataObject
    {
        
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private string MessageField;
        
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData
        {
            get
            {
                return this.extensionDataField;
            }
            set
            {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Message
        {
            get
            {
                return this.MessageField;
            }
            set
            {
                this.MessageField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="BaseContract", Namespace="urn:XLoc.Xml.Types")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.LocalizedString))]
    public partial class BaseContract : object, System.Runtime.Serialization.IExtensibleDataObject
    {
        
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private bool EncodedField;
        
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData
        {
            get
            {
                return this.extensionDataField;
            }
            set
            {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Encoded
        {
            get
            {
                return this.EncodedField;
            }
            set
            {
                this.EncodedField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ContextType", Namespace="urn:XLoc.Xml.Types")]
    public enum ContextType : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ItemSet = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Ability = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Statics = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Monster = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Quest = 4,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Inventory = 5,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Objective = 6,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Zone = 7,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ZoneArea = 8,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Condition = 9,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        OCondition = 10,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Waypoint = 11,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ETalk = 12,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ETalkString = 13,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        OQuest = 14,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Unknown = 15,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SearchCriteria", Namespace="urn:XLoc.Xml.Types")]
    public partial class SearchCriteria : object, System.Runtime.Serialization.IExtensibleDataObject
    {
        
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private XLoc.Xml.Types.FileGroup[] FileGroupsField;
        
        private XLoc.Xml.Types.GameFile[] GamesField;
        
        private bool IsCacheExpiredField;
        
        private XLoc.Xml.Types.Language[] LanguagesField;
        
        private XLoc.Xml.Types.Localization[] LocalizationsField;
        
        private XLoc.Xml.Types.Platform[] PlatformsField;
        
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData
        {
            get
            {
                return this.extensionDataField;
            }
            set
            {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public XLoc.Xml.Types.FileGroup[] FileGroups
        {
            get
            {
                return this.FileGroupsField;
            }
            set
            {
                this.FileGroupsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public XLoc.Xml.Types.GameFile[] Games
        {
            get
            {
                return this.GamesField;
            }
            set
            {
                this.GamesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsCacheExpired
        {
            get
            {
                return this.IsCacheExpiredField;
            }
            set
            {
                this.IsCacheExpiredField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public XLoc.Xml.Types.Language[] Languages
        {
            get
            {
                return this.LanguagesField;
            }
            set
            {
                this.LanguagesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public XLoc.Xml.Types.Localization[] Localizations
        {
            get
            {
                return this.LocalizationsField;
            }
            set
            {
                this.LocalizationsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public XLoc.Xml.Types.Platform[] Platforms
        {
            get
            {
                return this.PlatformsField;
            }
            set
            {
                this.PlatformsField = value;
            }
        }
    }
}


[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
[System.Runtime.Serialization.DataContractAttribute(Name="ContextListData", Namespace="")]
public partial class ContextListData : object, System.Runtime.Serialization.IExtensibleDataObject
{
    
    private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
    
    private ContextListLine[] LinesField;
    
    private int ResourceIdField;
    
    private System.Guid StringIdField;
    
    public System.Runtime.Serialization.ExtensionDataObject ExtensionData
    {
        get
        {
            return this.extensionDataField;
        }
        set
        {
            this.extensionDataField = value;
        }
    }
    
    [System.Runtime.Serialization.DataMemberAttribute()]
    public ContextListLine[] Lines
    {
        get
        {
            return this.LinesField;
        }
        set
        {
            this.LinesField = value;
        }
    }
    
    [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
    public int ResourceId
    {
        get
        {
            return this.ResourceIdField;
        }
        set
        {
            this.ResourceIdField = value;
        }
    }
    
    [System.Runtime.Serialization.DataMemberAttribute()]
    public System.Guid StringId
    {
        get
        {
            return this.StringIdField;
        }
        set
        {
            this.StringIdField = value;
        }
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
[System.Runtime.Serialization.DataContractAttribute(Name="ContextListLine", Namespace="")]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.Version[]))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.Version))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.SessionFault))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.Language[]))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.Language))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.Session))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.UserAccount))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.Localization[]))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.Localization))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.LocalizationType))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.GameFile[]))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.GameFile))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.ActiveMember))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.Platform))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.Platform[]))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.FileGroup[]))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.FileGroup))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.LocalizedString))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.BaseContract))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.SearchRequest))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.OrderBy))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.SearchResult))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.ContextType))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.LocalizedString[]))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(XLoc.Xml.Types.SearchCriteria))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(ContextListData[]))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(ContextListData))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(ContextListLine[]))]
[System.Runtime.Serialization.KnownTypeAttribute(typeof(ContextLineType))]
public partial class ContextListLine : object, System.Runtime.Serialization.IExtensibleDataObject
{
    
    private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
    
    private XLoc.Xml.Types.ContextType ContextTypeField;
    
    private object DataField;
    
    private ContextLineType LineTypeField;
    
    private string LinkTextField;
    
    private string LocalizedTextField;
    
    private XLoc.Xml.Types.LocalizedString ReferencedStringField;
    
    private string StringIdField;
    
    private string TextField;
    
    public System.Runtime.Serialization.ExtensionDataObject ExtensionData
    {
        get
        {
            return this.extensionDataField;
        }
        set
        {
            this.extensionDataField = value;
        }
    }
    
    [System.Runtime.Serialization.DataMemberAttribute()]
    public XLoc.Xml.Types.ContextType ContextType
    {
        get
        {
            return this.ContextTypeField;
        }
        set
        {
            this.ContextTypeField = value;
        }
    }
    
    [System.Runtime.Serialization.DataMemberAttribute()]
    public object Data
    {
        get
        {
            return this.DataField;
        }
        set
        {
            this.DataField = value;
        }
    }
    
    [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
    public ContextLineType LineType
    {
        get
        {
            return this.LineTypeField;
        }
        set
        {
            this.LineTypeField = value;
        }
    }
    
    [System.Runtime.Serialization.DataMemberAttribute()]
    public string LinkText
    {
        get
        {
            return this.LinkTextField;
        }
        set
        {
            this.LinkTextField = value;
        }
    }
    
    [System.Runtime.Serialization.DataMemberAttribute()]
    public string LocalizedText
    {
        get
        {
            return this.LocalizedTextField;
        }
        set
        {
            this.LocalizedTextField = value;
        }
    }
    
    [System.Runtime.Serialization.DataMemberAttribute()]
    public XLoc.Xml.Types.LocalizedString ReferencedString
    {
        get
        {
            return this.ReferencedStringField;
        }
        set
        {
            this.ReferencedStringField = value;
        }
    }
    
    [System.Runtime.Serialization.DataMemberAttribute()]
    public string StringId
    {
        get
        {
            return this.StringIdField;
        }
        set
        {
            this.StringIdField = value;
        }
    }
    
    [System.Runtime.Serialization.DataMemberAttribute()]
    public string Text
    {
        get
        {
            return this.TextField;
        }
        set
        {
            this.TextField = value;
        }
    }
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
[System.Runtime.Serialization.DataContractAttribute(Name="ContextLineType", Namespace="")]
public enum ContextLineType : int
{
    
    [System.Runtime.Serialization.EnumMemberAttribute()]
    Link = 0,
    
    [System.Runtime.Serialization.EnumMemberAttribute()]
    Text = 1,
    
    [System.Runtime.Serialization.EnumMemberAttribute()]
    Context = 2,
    
    [System.Runtime.Serialization.EnumMemberAttribute()]
    Header = 3,
    
    [System.Runtime.Serialization.EnumMemberAttribute()]
    NewLine = 4,
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
[System.ServiceModel.ServiceContractAttribute(Namespace="urn:XLoc.Services", ConfigurationName="IXLocApi", SessionMode=System.ServiceModel.SessionMode.NotAllowed)]
public interface IXLocApi
{
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetTopVersionedBuilds", ReplyAction="urn:XLoc.Services/IXLocApi/GetTopVersionedBuildsResponse")]
    string GetTopVersionedBuilds(string api_key, string auth_token, string api_sig, string localization_id, string language_id, string version_id, string max_records);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetTopVersionedBuilds", ReplyAction="urn:XLoc.Services/IXLocApi/GetTopVersionedBuildsResponse")]
    System.Threading.Tasks.Task<string> GetTopVersionedBuildsAsync(string api_key, string auth_token, string api_sig, string localization_id, string language_id, string version_id, string max_records);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetVersionedBuilds", ReplyAction="urn:XLoc.Services/IXLocApi/GetVersionedBuildsResponse")]
    string GetVersionedBuilds(string api_key, string auth_token, string api_sig, string localization_id, string language_id, string version_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetVersionedBuilds", ReplyAction="urn:XLoc.Services/IXLocApi/GetVersionedBuildsResponse")]
    System.Threading.Tasks.Task<string> GetVersionedBuildsAsync(string api_key, string auth_token, string api_sig, string localization_id, string language_id, string version_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetLatestVersionedBuild", ReplyAction="urn:XLoc.Services/IXLocApi/GetLatestVersionedBuildResponse")]
    string GetLatestVersionedBuild(string api_key, string auth_token, string api_sig, string localization_id, string language_id, string version_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetLatestVersionedBuild", ReplyAction="urn:XLoc.Services/IXLocApi/GetLatestVersionedBuildResponse")]
    System.Threading.Tasks.Task<string> GetLatestVersionedBuildAsync(string api_key, string auth_token, string api_sig, string localization_id, string language_id, string version_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetVersionsByLocalization", ReplyAction="urn:XLoc.Services/IXLocApi/GetVersionsByLocalizationResponse")]
    [System.ServiceModel.FaultContractAttribute(typeof(XLoc.Xml.Types.SessionFault), Action="urn:XLoc.Services/IXLocApi/GetVersionsByLocalizationSessionFaultFault", Name="SessionFault", Namespace="urn:XLoc.Xml.Types")]
    XLoc.Xml.Types.Version[] GetVersionsByLocalization(string api_key, string auth_token, string api_sig, string localization_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetVersionsByLocalization", ReplyAction="urn:XLoc.Services/IXLocApi/GetVersionsByLocalizationResponse")]
    System.Threading.Tasks.Task<XLoc.Xml.Types.Version[]> GetVersionsByLocalizationAsync(string api_key, string auth_token, string api_sig, string localization_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetLanguagesByLocalization", ReplyAction="urn:XLoc.Services/IXLocApi/GetLanguagesByLocalizationResponse")]
    [System.ServiceModel.FaultContractAttribute(typeof(XLoc.Xml.Types.SessionFault), Action="urn:XLoc.Services/IXLocApi/GetLanguagesByLocalizationSessionFaultFault", Name="SessionFault", Namespace="urn:XLoc.Xml.Types")]
    XLoc.Xml.Types.Language[] GetLanguagesByLocalization(string api_key, string auth_token, string api_sig, string localization_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetLanguagesByLocalization", ReplyAction="urn:XLoc.Services/IXLocApi/GetLanguagesByLocalizationResponse")]
    System.Threading.Tasks.Task<XLoc.Xml.Types.Language[]> GetLanguagesByLocalizationAsync(string api_key, string auth_token, string api_sig, string localization_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetLastUploadedGameFile", ReplyAction="urn:XLoc.Services/IXLocApi/GetLastUploadedGameFileResponse")]
    System.Guid GetLastUploadedGameFile(string api_key, string auth_token, string api_sig);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetLastUploadedGameFile", ReplyAction="urn:XLoc.Services/IXLocApi/GetLastUploadedGameFileResponse")]
    System.Threading.Tasks.Task<System.Guid> GetLastUploadedGameFileAsync(string api_key, string auth_token, string api_sig);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetBuild", ReplyAction="urn:XLoc.Services/IXLocApi/GetBuildResponse")]
    string GetBuild(string api_key, string auth_token, string api_sig, string build_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetBuild", ReplyAction="urn:XLoc.Services/IXLocApi/GetBuildResponse")]
    System.Threading.Tasks.Task<string> GetBuildAsync(string api_key, string auth_token, string api_sig, string build_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetBuilds", ReplyAction="urn:XLoc.Services/IXLocApi/GetBuildsResponse")]
    string GetBuilds(string api_key, string auth_token, string api_sig, string localization_id, string language_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetBuilds", ReplyAction="urn:XLoc.Services/IXLocApi/GetBuildsResponse")]
    System.Threading.Tasks.Task<string> GetBuildsAsync(string api_key, string auth_token, string api_sig, string localization_id, string language_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/DownloadBuild", ReplyAction="urn:XLoc.Services/IXLocApi/DownloadBuildResponse")]
    byte[] DownloadBuild(string api_key, string auth_token, string api_sig, string build_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/DownloadBuild", ReplyAction="urn:XLoc.Services/IXLocApi/DownloadBuildResponse")]
    System.Threading.Tasks.Task<byte[]> DownloadBuildAsync(string api_key, string auth_token, string api_sig, string build_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetLatestBuild", ReplyAction="urn:XLoc.Services/IXLocApi/GetLatestBuildResponse")]
    string GetLatestBuild(string api_key, string auth_token, string api_sig, string localization_id, string language_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetLatestBuild", ReplyAction="urn:XLoc.Services/IXLocApi/GetLatestBuildResponse")]
    System.Threading.Tasks.Task<string> GetLatestBuildAsync(string api_key, string auth_token, string api_sig, string localization_id, string language_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetLatestBuildByFile", ReplyAction="urn:XLoc.Services/IXLocApi/GetLatestBuildByFileResponse")]
    string GetLatestBuildByFile(string api_key, string auth_token, string api_sig, string localization_id, string language_id, string filename);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetLatestBuildByFile", ReplyAction="urn:XLoc.Services/IXLocApi/GetLatestBuildByFileResponse")]
    System.Threading.Tasks.Task<string> GetLatestBuildByFileAsync(string api_key, string auth_token, string api_sig, string localization_id, string language_id, string filename);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/Build", ReplyAction="urn:XLoc.Services/IXLocApi/BuildResponse")]
    string Build(string api_key, string auth_token, string api_sig, string localization_id, string language_id, string platform_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/Build", ReplyAction="urn:XLoc.Services/IXLocApi/BuildResponse")]
    System.Threading.Tasks.Task<string> BuildAsync(string api_key, string auth_token, string api_sig, string localization_id, string language_id, string platform_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/IsSessionActive", ReplyAction="urn:XLoc.Services/IXLocApi/IsSessionActiveResponse")]
    bool IsSessionActive(string api_key, string auth_token, string api_sig);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/IsSessionActive", ReplyAction="urn:XLoc.Services/IXLocApi/IsSessionActiveResponse")]
    System.Threading.Tasks.Task<bool> IsSessionActiveAsync(string api_key, string auth_token, string api_sig);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetAuthToken", ReplyAction="urn:XLoc.Services/IXLocApi/GetAuthTokenResponse")]
    [System.ServiceModel.FaultContractAttribute(typeof(XLoc.Xml.Types.SessionFault), Action="urn:XLoc.Services/IXLocApi/GetAuthTokenSessionFaultFault", Name="SessionFault", Namespace="urn:XLoc.Xml.Types")]
    string GetAuthToken(string api_key, string api_sig);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetAuthToken", ReplyAction="urn:XLoc.Services/IXLocApi/GetAuthTokenResponse")]
    System.Threading.Tasks.Task<string> GetAuthTokenAsync(string api_key, string api_sig);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetAuthTokenByUser", ReplyAction="urn:XLoc.Services/IXLocApi/GetAuthTokenByUserResponse")]
    [System.ServiceModel.FaultContractAttribute(typeof(XLoc.Xml.Types.SessionFault), Action="urn:XLoc.Services/IXLocApi/GetAuthTokenByUserSessionFaultFault", Name="SessionFault", Namespace="urn:XLoc.Xml.Types")]
    XLoc.Xml.Types.Session GetAuthTokenByUser(string api_key, string user_name, string pwd, string api_sig);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetAuthTokenByUser", ReplyAction="urn:XLoc.Services/IXLocApi/GetAuthTokenByUserResponse")]
    System.Threading.Tasks.Task<XLoc.Xml.Types.Session> GetAuthTokenByUserAsync(string api_key, string user_name, string pwd, string api_sig);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetLocalizations", ReplyAction="urn:XLoc.Services/IXLocApi/GetLocalizationsResponse")]
    [System.ServiceModel.FaultContractAttribute(typeof(XLoc.Xml.Types.SessionFault), Action="urn:XLoc.Services/IXLocApi/GetLocalizationsSessionFaultFault", Name="SessionFault", Namespace="urn:XLoc.Xml.Types")]
    XLoc.Xml.Types.Localization[] GetLocalizations(string api_key, string auth_token, string api_sig);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetLocalizations", ReplyAction="urn:XLoc.Services/IXLocApi/GetLocalizationsResponse")]
    System.Threading.Tasks.Task<XLoc.Xml.Types.Localization[]> GetLocalizationsAsync(string api_key, string auth_token, string api_sig);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetLocalizationsWithContext", ReplyAction="urn:XLoc.Services/IXLocApi/GetLocalizationsWithContextResponse")]
    [System.ServiceModel.FaultContractAttribute(typeof(XLoc.Xml.Types.SessionFault), Action="urn:XLoc.Services/IXLocApi/GetLocalizationsWithContextSessionFaultFault", Name="SessionFault", Namespace="urn:XLoc.Xml.Types")]
    XLoc.Xml.Types.Localization[] GetLocalizationsWithContext(string api_key, string auth_token, string api_sig);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetLocalizationsWithContext", ReplyAction="urn:XLoc.Services/IXLocApi/GetLocalizationsWithContextResponse")]
    System.Threading.Tasks.Task<XLoc.Xml.Types.Localization[]> GetLocalizationsWithContextAsync(string api_key, string auth_token, string api_sig);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetLanguages", ReplyAction="urn:XLoc.Services/IXLocApi/GetLanguagesResponse")]
    [System.ServiceModel.FaultContractAttribute(typeof(XLoc.Xml.Types.SessionFault), Action="urn:XLoc.Services/IXLocApi/GetLanguagesSessionFaultFault", Name="SessionFault", Namespace="urn:XLoc.Xml.Types")]
    XLoc.Xml.Types.Language[] GetLanguages(string api_key, string auth_token, string api_sig);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetLanguages", ReplyAction="urn:XLoc.Services/IXLocApi/GetLanguagesResponse")]
    System.Threading.Tasks.Task<XLoc.Xml.Types.Language[]> GetLanguagesAsync(string api_key, string auth_token, string api_sig);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetGameFiles", ReplyAction="urn:XLoc.Services/IXLocApi/GetGameFilesResponse")]
    [System.ServiceModel.FaultContractAttribute(typeof(XLoc.Xml.Types.SessionFault), Action="urn:XLoc.Services/IXLocApi/GetGameFilesSessionFaultFault", Name="SessionFault", Namespace="urn:XLoc.Xml.Types")]
    string GetGameFiles(string api_key, string auth_token, string api_sig, string localization_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetGameFiles", ReplyAction="urn:XLoc.Services/IXLocApi/GetGameFilesResponse")]
    System.Threading.Tasks.Task<string> GetGameFilesAsync(string api_key, string auth_token, string api_sig, string localization_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetPlatforms", ReplyAction="urn:XLoc.Services/IXLocApi/GetPlatformsResponse")]
    [System.ServiceModel.FaultContractAttribute(typeof(XLoc.Xml.Types.SessionFault), Action="urn:XLoc.Services/IXLocApi/GetPlatformsSessionFaultFault", Name="SessionFault", Namespace="urn:XLoc.Xml.Types")]
    string GetPlatforms(string api_key, string auth_token, string api_sig, string localization_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetPlatforms", ReplyAction="urn:XLoc.Services/IXLocApi/GetPlatformsResponse")]
    System.Threading.Tasks.Task<string> GetPlatformsAsync(string api_key, string auth_token, string api_sig, string localization_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetFileGroups", ReplyAction="urn:XLoc.Services/IXLocApi/GetFileGroupsResponse")]
    [System.ServiceModel.FaultContractAttribute(typeof(XLoc.Xml.Types.SessionFault), Action="urn:XLoc.Services/IXLocApi/GetFileGroupsSessionFaultFault", Name="SessionFault", Namespace="urn:XLoc.Xml.Types")]
    string GetFileGroups(string api_key, string auth_token, string api_sig, string localization_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetFileGroups", ReplyAction="urn:XLoc.Services/IXLocApi/GetFileGroupsResponse")]
    System.Threading.Tasks.Task<string> GetFileGroupsAsync(string api_key, string auth_token, string api_sig, string localization_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetStrings", ReplyAction="urn:XLoc.Services/IXLocApi/GetStringsResponse")]
    [System.ServiceModel.FaultContractAttribute(typeof(XLoc.Xml.Types.SessionFault), Action="urn:XLoc.Services/IXLocApi/GetStringsSessionFaultFault", Name="SessionFault", Namespace="urn:XLoc.Xml.Types")]
    XLoc.Xml.Types.SearchResult GetStrings(string api_key, string auth_token, string api_sig, XLoc.Xml.Types.SearchRequest oSearchRequest);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetStrings", ReplyAction="urn:XLoc.Services/IXLocApi/GetStringsResponse")]
    System.Threading.Tasks.Task<XLoc.Xml.Types.SearchResult> GetStringsAsync(string api_key, string auth_token, string api_sig, XLoc.Xml.Types.SearchRequest oSearchRequest);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetSearchCriteria", ReplyAction="urn:XLoc.Services/IXLocApi/GetSearchCriteriaResponse")]
    [System.ServiceModel.FaultContractAttribute(typeof(XLoc.Xml.Types.SessionFault), Action="urn:XLoc.Services/IXLocApi/GetSearchCriteriaSessionFaultFault", Name="SessionFault", Namespace="urn:XLoc.Xml.Types")]
    XLoc.Xml.Types.SearchCriteria GetSearchCriteria(string api_key, string auth_token, string api_sig, string localization_id);
    
    [System.ServiceModel.OperationContractAttribute(Action="urn:XLoc.Services/IXLocApi/GetSearchCriteria", ReplyAction="urn:XLoc.Services/IXLocApi/GetSearchCriteriaResponse")]
    System.Threading.Tasks.Task<XLoc.Xml.Types.SearchCriteria> GetSearchCriteriaAsync(string api_key, string auth_token, string api_sig, string localization_id);
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
public interface IXLocApiChannel : IXLocApi, System.ServiceModel.IClientChannel
{
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
public partial class XLocApiClient : System.ServiceModel.ClientBase<IXLocApi>, IXLocApi
{
    
    public XLocApiClient()
    {
    }
    
    public XLocApiClient(string endpointConfigurationName) : 
            base(endpointConfigurationName)
    {
    }
    
    public XLocApiClient(string endpointConfigurationName, string remoteAddress) : 
            base(endpointConfigurationName, remoteAddress)
    {
    }
    
    public XLocApiClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
            base(endpointConfigurationName, remoteAddress)
    {
    }
    
    public XLocApiClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
            base(binding, remoteAddress)
    {
    }
    
    public string GetTopVersionedBuilds(string api_key, string auth_token, string api_sig, string localization_id, string language_id, string version_id, string max_records)
    {
        return base.Channel.GetTopVersionedBuilds(api_key, auth_token, api_sig, localization_id, language_id, version_id, max_records);
    }
    
    public System.Threading.Tasks.Task<string> GetTopVersionedBuildsAsync(string api_key, string auth_token, string api_sig, string localization_id, string language_id, string version_id, string max_records)
    {
        return base.Channel.GetTopVersionedBuildsAsync(api_key, auth_token, api_sig, localization_id, language_id, version_id, max_records);
    }
    
    public string GetVersionedBuilds(string api_key, string auth_token, string api_sig, string localization_id, string language_id, string version_id)
    {
        return base.Channel.GetVersionedBuilds(api_key, auth_token, api_sig, localization_id, language_id, version_id);
    }
    
    public System.Threading.Tasks.Task<string> GetVersionedBuildsAsync(string api_key, string auth_token, string api_sig, string localization_id, string language_id, string version_id)
    {
        return base.Channel.GetVersionedBuildsAsync(api_key, auth_token, api_sig, localization_id, language_id, version_id);
    }
    
    public string GetLatestVersionedBuild(string api_key, string auth_token, string api_sig, string localization_id, string language_id, string version_id)
    {
        return base.Channel.GetLatestVersionedBuild(api_key, auth_token, api_sig, localization_id, language_id, version_id);
    }
    
    public System.Threading.Tasks.Task<string> GetLatestVersionedBuildAsync(string api_key, string auth_token, string api_sig, string localization_id, string language_id, string version_id)
    {
        return base.Channel.GetLatestVersionedBuildAsync(api_key, auth_token, api_sig, localization_id, language_id, version_id);
    }
    
    public XLoc.Xml.Types.Version[] GetVersionsByLocalization(string api_key, string auth_token, string api_sig, string localization_id)
    {
        return base.Channel.GetVersionsByLocalization(api_key, auth_token, api_sig, localization_id);
    }
    
    public System.Threading.Tasks.Task<XLoc.Xml.Types.Version[]> GetVersionsByLocalizationAsync(string api_key, string auth_token, string api_sig, string localization_id)
    {
        return base.Channel.GetVersionsByLocalizationAsync(api_key, auth_token, api_sig, localization_id);
    }
    
    public XLoc.Xml.Types.Language[] GetLanguagesByLocalization(string api_key, string auth_token, string api_sig, string localization_id)
    {
        return base.Channel.GetLanguagesByLocalization(api_key, auth_token, api_sig, localization_id);
    }
    
    public System.Threading.Tasks.Task<XLoc.Xml.Types.Language[]> GetLanguagesByLocalizationAsync(string api_key, string auth_token, string api_sig, string localization_id)
    {
        return base.Channel.GetLanguagesByLocalizationAsync(api_key, auth_token, api_sig, localization_id);
    }
    
    public System.Guid GetLastUploadedGameFile(string api_key, string auth_token, string api_sig)
    {
        return base.Channel.GetLastUploadedGameFile(api_key, auth_token, api_sig);
    }
    
    public System.Threading.Tasks.Task<System.Guid> GetLastUploadedGameFileAsync(string api_key, string auth_token, string api_sig)
    {
        return base.Channel.GetLastUploadedGameFileAsync(api_key, auth_token, api_sig);
    }
    
    public string GetBuild(string api_key, string auth_token, string api_sig, string build_id)
    {
        return base.Channel.GetBuild(api_key, auth_token, api_sig, build_id);
    }
    
    public System.Threading.Tasks.Task<string> GetBuildAsync(string api_key, string auth_token, string api_sig, string build_id)
    {
        return base.Channel.GetBuildAsync(api_key, auth_token, api_sig, build_id);
    }
    
    public string GetBuilds(string api_key, string auth_token, string api_sig, string localization_id, string language_id)
    {
        return base.Channel.GetBuilds(api_key, auth_token, api_sig, localization_id, language_id);
    }
    
    public System.Threading.Tasks.Task<string> GetBuildsAsync(string api_key, string auth_token, string api_sig, string localization_id, string language_id)
    {
        return base.Channel.GetBuildsAsync(api_key, auth_token, api_sig, localization_id, language_id);
    }
    
    public byte[] DownloadBuild(string api_key, string auth_token, string api_sig, string build_id)
    {
        return base.Channel.DownloadBuild(api_key, auth_token, api_sig, build_id);
    }
    
    public System.Threading.Tasks.Task<byte[]> DownloadBuildAsync(string api_key, string auth_token, string api_sig, string build_id)
    {
        return base.Channel.DownloadBuildAsync(api_key, auth_token, api_sig, build_id);
    }
    
    public string GetLatestBuild(string api_key, string auth_token, string api_sig, string localization_id, string language_id)
    {
        return base.Channel.GetLatestBuild(api_key, auth_token, api_sig, localization_id, language_id);
    }
    
    public System.Threading.Tasks.Task<string> GetLatestBuildAsync(string api_key, string auth_token, string api_sig, string localization_id, string language_id)
    {
        return base.Channel.GetLatestBuildAsync(api_key, auth_token, api_sig, localization_id, language_id);
    }
    
    public string GetLatestBuildByFile(string api_key, string auth_token, string api_sig, string localization_id, string language_id, string filename)
    {
        return base.Channel.GetLatestBuildByFile(api_key, auth_token, api_sig, localization_id, language_id, filename);
    }
    
    public System.Threading.Tasks.Task<string> GetLatestBuildByFileAsync(string api_key, string auth_token, string api_sig, string localization_id, string language_id, string filename)
    {
        return base.Channel.GetLatestBuildByFileAsync(api_key, auth_token, api_sig, localization_id, language_id, filename);
    }
    
    public string Build(string api_key, string auth_token, string api_sig, string localization_id, string language_id, string platform_id)
    {
        return base.Channel.Build(api_key, auth_token, api_sig, localization_id, language_id, platform_id);
    }
    
    public System.Threading.Tasks.Task<string> BuildAsync(string api_key, string auth_token, string api_sig, string localization_id, string language_id, string platform_id)
    {
        return base.Channel.BuildAsync(api_key, auth_token, api_sig, localization_id, language_id, platform_id);
    }
    
    public bool IsSessionActive(string api_key, string auth_token, string api_sig)
    {
        return base.Channel.IsSessionActive(api_key, auth_token, api_sig);
    }
    
    public System.Threading.Tasks.Task<bool> IsSessionActiveAsync(string api_key, string auth_token, string api_sig)
    {
        return base.Channel.IsSessionActiveAsync(api_key, auth_token, api_sig);
    }
    
    public string GetAuthToken(string api_key, string api_sig)
    {
        return base.Channel.GetAuthToken(api_key, api_sig);
    }
    
    public System.Threading.Tasks.Task<string> GetAuthTokenAsync(string api_key, string api_sig)
    {
        return base.Channel.GetAuthTokenAsync(api_key, api_sig);
    }
    
    public XLoc.Xml.Types.Session GetAuthTokenByUser(string api_key, string user_name, string pwd, string api_sig)
    {
        return base.Channel.GetAuthTokenByUser(api_key, user_name, pwd, api_sig);
    }
    
    public System.Threading.Tasks.Task<XLoc.Xml.Types.Session> GetAuthTokenByUserAsync(string api_key, string user_name, string pwd, string api_sig)
    {
        return base.Channel.GetAuthTokenByUserAsync(api_key, user_name, pwd, api_sig);
    }
    
    public XLoc.Xml.Types.Localization[] GetLocalizations(string api_key, string auth_token, string api_sig)
    {
        return base.Channel.GetLocalizations(api_key, auth_token, api_sig);
    }
    
    public System.Threading.Tasks.Task<XLoc.Xml.Types.Localization[]> GetLocalizationsAsync(string api_key, string auth_token, string api_sig)
    {
        return base.Channel.GetLocalizationsAsync(api_key, auth_token, api_sig);
    }
    
    public XLoc.Xml.Types.Localization[] GetLocalizationsWithContext(string api_key, string auth_token, string api_sig)
    {
        return base.Channel.GetLocalizationsWithContext(api_key, auth_token, api_sig);
    }
    
    public System.Threading.Tasks.Task<XLoc.Xml.Types.Localization[]> GetLocalizationsWithContextAsync(string api_key, string auth_token, string api_sig)
    {
        return base.Channel.GetLocalizationsWithContextAsync(api_key, auth_token, api_sig);
    }
    
    public XLoc.Xml.Types.Language[] GetLanguages(string api_key, string auth_token, string api_sig)
    {
        return base.Channel.GetLanguages(api_key, auth_token, api_sig);
    }
    
    public System.Threading.Tasks.Task<XLoc.Xml.Types.Language[]> GetLanguagesAsync(string api_key, string auth_token, string api_sig)
    {
        return base.Channel.GetLanguagesAsync(api_key, auth_token, api_sig);
    }
    
    public string GetGameFiles(string api_key, string auth_token, string api_sig, string localization_id)
    {
        return base.Channel.GetGameFiles(api_key, auth_token, api_sig, localization_id);
    }
    
    public System.Threading.Tasks.Task<string> GetGameFilesAsync(string api_key, string auth_token, string api_sig, string localization_id)
    {
        return base.Channel.GetGameFilesAsync(api_key, auth_token, api_sig, localization_id);
    }
    
    public string GetPlatforms(string api_key, string auth_token, string api_sig, string localization_id)
    {
        return base.Channel.GetPlatforms(api_key, auth_token, api_sig, localization_id);
    }
    
    public System.Threading.Tasks.Task<string> GetPlatformsAsync(string api_key, string auth_token, string api_sig, string localization_id)
    {
        return base.Channel.GetPlatformsAsync(api_key, auth_token, api_sig, localization_id);
    }
    
    public string GetFileGroups(string api_key, string auth_token, string api_sig, string localization_id)
    {
        return base.Channel.GetFileGroups(api_key, auth_token, api_sig, localization_id);
    }
    
    public System.Threading.Tasks.Task<string> GetFileGroupsAsync(string api_key, string auth_token, string api_sig, string localization_id)
    {
        return base.Channel.GetFileGroupsAsync(api_key, auth_token, api_sig, localization_id);
    }
    
    public XLoc.Xml.Types.SearchResult GetStrings(string api_key, string auth_token, string api_sig, XLoc.Xml.Types.SearchRequest oSearchRequest)
    {
        return base.Channel.GetStrings(api_key, auth_token, api_sig, oSearchRequest);
    }
    
    public System.Threading.Tasks.Task<XLoc.Xml.Types.SearchResult> GetStringsAsync(string api_key, string auth_token, string api_sig, XLoc.Xml.Types.SearchRequest oSearchRequest)
    {
        return base.Channel.GetStringsAsync(api_key, auth_token, api_sig, oSearchRequest);
    }
    
    public XLoc.Xml.Types.SearchCriteria GetSearchCriteria(string api_key, string auth_token, string api_sig, string localization_id)
    {
        return base.Channel.GetSearchCriteria(api_key, auth_token, api_sig, localization_id);
    }
    
    public System.Threading.Tasks.Task<XLoc.Xml.Types.SearchCriteria> GetSearchCriteriaAsync(string api_key, string auth_token, string api_sig, string localization_id)
    {
        return base.Channel.GetSearchCriteriaAsync(api_key, auth_token, api_sig, localization_id);
    }
}
